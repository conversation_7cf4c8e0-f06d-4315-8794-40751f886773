.layout {
  min-height: 100vh;
  background: linear-gradient(to bottom, #e8f1ff, #f6f7f9);
}

.header {
  height: 56px;
  line-height: 56px;
  padding-inline: 24px;
  background-color: #e8f1ff;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  border-bottom: 1px solid #d7e1eb;
}

.title {
  font-size: 16px;
  font-weight: bold;
}

.nav {
  display: flex;
  gap: 64px;
  margin-left: 180px;
}

.nav a {
  font-size: 14px;
  opacity: 0.65;
}

.nav a.active {
  font-weight: bold;
  opacity: 1;
}

.connectTip {
  padding: 24px;
  text-align: center;
}
